import http from "@/utils/http";

// 发送验证码
// 103019 手机号码不存在
// 103039 手机号格式不对
// 103040 手机号被封
// 如果 type=  13,14,15,3,20,21,19 会返回103031 要先绑定手机号
// type=12 可能会返回 102031 手机号已经被绑定
// 1 type 类型错误
// 102041 短信发送失败
// 102040 短信发送失败
// 200 成功
export const sendCodeMsg = (data = {}) => {
  return http.post("/common/api/sms/send/short/msg", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
//  绑定手机号时是否发送邮件通知
export const isBindSend = (data = {}) => {
  return http.post("/common/api/is/bind/send", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

//  更新phone
export const changePhone = (data = {}) => {
  return http.post("/common/api/update/bind/phone", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
//  绑定phone
export const bindPhone = (data = {}) => {
  return http.post("/common/api/player/add-bind", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// 重置密码验证code
export const verifyCode = (data = {}) => {
  return http.post("/common/api/sms/verify/short/msg/code", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
