<template>
  <div>
    <!-- 验证码登录 手机号-->
    <div v-show="!loginStore.isCodeInputMode" class="step1">
      <PhoneInput
        ref="phoneInputRef"
        v-model="loginStore.userPhone"
        :validateOnBlur="false"
        :validateOnInput="false"
      />
      <ZButton :click="goSendCode" class="btn"> Register / Log in </ZButton>
      <div
        @click="checkLoginTypeClick"
        class="form-redline"
        v-if="globalStore.loginConfig.login_password == 0"
      >
        Login With Password
        <ZIcon type="icon-qianjin" color=""></ZIcon>
      </div>
    </div>
    <!-- 验证码 -->
    <div class="step2" v-show="loginStore.isCodeInputMode">
      <div class="form-line">
        A text messsage with a 6-digit code was just sent to
        <div class="phone">{{ maskString(loginStore.userPhone + "") }}</div>
      </div>
      <div class="input-wrap">
        <VerificationCodeInput ref="verCodeInputRef" v-model="verCode"></VerificationCodeInput>
        <CodeCountdownButton ref="countdownButtonRef" @click="getCode" />
      </div>
      <ZButton class="btn" @click="codeLogin"> Log in </ZButton>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { sendCodeMsg } from "@/api/user";
import { showToast } from "vant";
import { maskString } from "@/utils/core/tools";

import { ref, onUnmounted } from "vue";
import PhoneInput from "./PhoneInput.vue";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";

import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { useGlobalStore } from "@/stores/global";
import { useLoginStore } from "@/stores/login";

const globalStore = useGlobalStore();
const loginStore = useLoginStore();

// 响应式数据
const verCode = ref("");
const countdownButtonRef = ref();
const phoneInputRef = ref();
const verCodeInputRef = ref();

// 验证码发送状态
const hasSendMsg = ref(false);

const checkLoginTypeClick = () => {
  loginStore.switchToPasswordLogin(loginStore.userPhone);
};

// 获取验证码
const getCode = async (type = "phone_login_code") => {
  try {
    await loginStore.handleGeetestVerification(type as any, async (ret: any) => {
      const params = {
        phone: loginStore.userPhone,
        telephoneCode: "+63",
        type: type === "phone_login_code" ? "1" : "3",
        ...ret,
      };

      showZLoading();

      try {
        await sendCodeMsg(params);
        hasSendMsg.value = true;
        loginStore.isCodeInputMode = true;
        showToast("Verification code sent successfully");

        // 验证码发送成功后，手动开始倒计时
        if (countdownButtonRef.value) {
          countdownButtonRef.value.start();
        }
      } catch (sendError) {
        // 使用组件内置的失败处理方法
        if (countdownButtonRef.value) {
          countdownButtonRef.value.handleSendFailed();
        }
        throw sendError; // 重新抛出错误，让外层catch处理
      } finally {
        closeZLoading(); // 确保无论成功还是失败都关闭loading
      }
    });
  } catch (error) {
    console.log("error", error);
    console.error("Get code error:", error);
    // 这里不需要再次调用closeZLoading，因为内层finally已经处理了
  }
};

const codeLogin = async () => {
  // 在点击登录时进行校验

  // 1. 使用手机号组件的内部验证
  phoneInputRef.value?.validatePhone();
  if (!phoneInputRef.value?.isValid()) {
    return;
  }

  // 2. 使用验证码组件的内部验证
  if (!verCodeInputRef.value?.validate()) {
    return;
  }

  // 3. 校验是否已发送验证码
  if (!hasSendMsg.value) {
    verCodeInputRef.value?.showError("Please get the verification code first");
    return;
  }

  try {
    await loginStore.handleCodeLogin({
      verCode: verCode.value,
      phone: loginStore.userPhone,
    });
  } catch (error) {
    console.error("Code login error:", error);
    // 错误处理由 store 处理
  }
};

// 去发送验证码页面
const goSendCode = () => {
  // 使用手机号组件的内部验证
  phoneInputRef.value?.validatePhone();
  if (!phoneInputRef.value?.isValid()) {
    return;
  }

  // 检查隐私协议
  if (!loginStore.isPrivacyAgreed) {
    // 弹出隐私协议弹窗
    loginStore.isPrivacyDialogVisible = true;
    return;
  }

  // 校验通过，切换到验证码输入界面
  loginStore.isCodeInputMode = true;
};

// 组件销毁时清理
onUnmounted(() => {
  verCode.value = "";
  hasSendMsg.value = false;
});
</script>

<style lang="scss" scoped>
.step1 {
  .form-redline {
    text-align: center;
    margin-top: 16px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    cursor: pointer;
  }

  .btn {
    margin-top: 28px;
    height: 48px;
  }
}

.step2 {
  .form-line {
    margin-bottom: 16px;
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    .phone {
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-top: 10px;
    }
  }

  .btn {
    margin-top: 30px;
    height: 48px;
  }

  /* 获取验证码按钮区域 */
  .input-wrap {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
  }
}
</style>
