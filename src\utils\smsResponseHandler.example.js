/**
 * SMS响应处理统一方案使用示例
 *
 * 这个文件展示了如何使用合并后的统一SMS响应处理函数
 * handleSmsResponse 现在同时支持 disposeSendMsgResponse 和完整响应处理功能
 */

import { sendCodeMsg } from "@/api/setPhoneNumber";
import { handleSmsResponse } from "@/utils/http/util";

// ========== 合并后的统一函数使用方式 ==========

/**
 * 示例0: 仅获取错误消息 (替代原 disposeSendMsgResponse)
 */
export const getErrorMessageExample = () => {
  // 方式1: 直接传入错误码 (兼容旧用法)
  const errorMsg1 = handleSmsResponse(103019); // 返回: "The Number does not exist."

  // 方式2: 传入完整响应对象，仅获取消息
  const response = { code: 103039, msg: "Custom message" };
  const errorMsg2 = handleSmsResponse(response, { onlyGetMessage: true });

  console.log(errorMsg1, errorMsg2);
};

// ========== 方案1: 使用新的统一处理函数 (推荐) ==========

/**
 * 示例1: 基本用法 - 自动处理成功/失败状态和toast提示
 */
export const sendCodeExample1 = async (params, countdownButtonRef) => {
  try {
    const response = await sendCodeMsg(params);

    // 使用统一处理函数
    const success = handleSmsResponse(response, {
      countdownRef: countdownButtonRef,
      onSuccess: (data) => {
        console.log("验证码发送成功", data);
        // 可以在这里添加额外的成功处理逻辑
      },
      onError: (code, message) => {
        console.error("验证码发送失败", code, message);
        // 可以在这里添加额外的错误处理逻辑
      },
    });

    return success;
  } catch (error) {
    console.error("API调用失败:", error);
    return false;
  }
};

/**
 * 示例2: 自定义处理 - 不显示默认toast，使用自定义处理
 */
export const sendCodeExample2 = async (params, countdownButtonRef, customSuccessCallback) => {
  try {
    const response = await sendCodeMsg(params);

    const success = handleSmsResponse(response, {
      countdownRef: countdownButtonRef,
      showToast: false, // 不显示默认toast
      onSuccess: (data) => {
        // 自定义成功处理
        customSuccessCallback?.();
        // 可以显示自定义的成功提示
      },
      onError: (code, message) => {
        // 自定义错误处理
        console.error("发送失败:", message);
        // 可以显示自定义的错误提示或执行其他逻辑
      },
    });

    return success;
  } catch (error) {
    console.error("API调用失败:", error);
    return false;
  }
};

// ========== 方案2: 在API层面统一处理 (已实现) ==========

/**
 * 示例3: 使用修改后的sendCodeMsg API - 自动抛出错误
 * 这种方式API会自动处理错误码并抛出相应的错误
 */
export const sendCodeExample3 = async (params, countdownButtonRef) => {
  try {
    // 修改后的sendCodeMsg会自动处理错误码
    // 成功时返回data，失败时抛出Error
    const data = await sendCodeMsg(params);

    // 成功处理
    import("vant").then(({ showToast }) => {
      showToast("验证码发送成功");
    });

    if (countdownButtonRef?.value) {
      countdownButtonRef.value.start();
    }

    return true;
  } catch (error) {
    // 错误处理 - error.message包含具体的错误信息
    import("vant").then(({ showToast }) => {
      showToast(error.message);
    });

    if (countdownButtonRef?.value) {
      countdownButtonRef.value.handleSendFailed();
    }

    return false;
  }
};

// ========== 迁移指南 ==========

/**
 * 旧的处理方式 (需要替换):
 *
 * // 方式A: 使用 disposeSendMsgResponse
 * const { code, msg } = await sendCodeMsg(params);
 * if (code === 200) {
 *   showToast(enTranslations.sendCodeMsgTip7);
 *   if (countdownButtonRef.value) {
 *     countdownButtonRef.value.start();
 *   }
 * } else {
 *   if (code === 600) {
 *     showToast(enTranslations.tipword23);
 *   } else {
 *     const errorTip = disposeSendMsgResponse(code);
 *     if (errorTip) {
 *       showToast(errorTip);
 *     } else if (countdownButtonRef.value) {
 *       countdownButtonRef.value.handleSendFailed();
 *     }
 *   }
 * }
 */

/**
 * 新的处理方式 (推荐):
 *
 * // 方式1: 使用合并后的统一处理函数 (最推荐)
 * const response = await sendCodeMsg(params);
 * handleSmsResponse(response, { countdownRef: countdownButtonRef });
 *
 * // 方式2: 仅获取错误消息 (替代 disposeSendMsgResponse)
 * const errorMsg = handleSmsResponse(103019); // 直接传错误码
 *
 * // 方式3: 使用try-catch (如果API层面已统一处理)
 * try {
 *   await sendCodeMsg(params);
 *   // 成功处理
 * } catch (error) {
 *   // 错误处理
 * }
 */

// ========== 实际迁移示例 ==========

/**
 * 迁移前: VerifyDialogChangePhone.vue 中的代码
 */
export const beforeMigration = async (params, countdownButtonRef) => {
  const { code, msg } = await sendCodeMsg(params);
  if (code === 200) {
    import("vant").then(({ showToast }) => {
      showToast("验证码发送成功");
    });
    if (countdownButtonRef.value) {
      countdownButtonRef.value.start();
    }
  } else {
    if (code === 600) {
      import("vant").then(({ showToast }) => {
        showToast("特殊错误");
      });
    } else {
      // 使用旧的 disposeSendMsgResponse
      const errorTip = handleSmsResponse(code); // 现在兼容旧用法
      if (errorTip) {
        import("vant").then(({ showToast }) => {
          showToast(errorTip);
        });
        return;
      }
      if (countdownButtonRef.value) {
        countdownButtonRef.value.handleSendFailed();
      }
    }
  }
};

/**
 * 迁移后: 使用合并后的统一函数
 */
export const afterMigration = async (params, countdownButtonRef) => {
  const response = await sendCodeMsg(params);

  // 一行代码完成所有处理
  handleSmsResponse(response, {
    countdownRef: countdownButtonRef,
    onSuccess: () => {
      console.log("验证码发送成功");
    },
    onError: (code, message) => {
      console.error("发送失败:", code, message);
    },
  });
};

// ========== 组件中的使用示例 ==========

/**
 * Vue组件中的使用示例
 */
export const vueComponentExample = {
  setup() {
    const countdownButtonRef = ref(null);

    const handleSendCode = async () => {
      const params = {
        phone: "1234567890",
        telephoneCode: "+63",
        type: "1",
      };

      // 使用统一处理函数
      const response = await sendCodeMsg(params);
      const success = handleSmsResponse(response, {
        countdownRef: countdownButtonRef,
        onSuccess: () => {
          // 额外的成功处理逻辑
          console.log("验证码发送成功");
        },
      });

      return success;
    };

    return {
      countdownButtonRef,
      handleSendCode,
    };
  },
};
